from app.common.helpers.logger import get_logger
from app.integrations.adapters.hubspot.client import HubSpotClient
from app.integrations.base.crm_account_access_resolver import ICRMAccountAccessResolver
from app.integrations.schemas import CRMAccountAccessData

logger = get_logger()


class HubSpotAccountAccessResolver(ICRMAccountAccessResolver):
    """
    HubSpot access resolver that retrieves:
    - Companies the user owns (hubspot_owner_id matches user ID)
    - All companies (if user has broad access permissions)

    Note: HubSpot has a simpler permission model compared to Salesforce.
    There's no equivalent to Salesforce's Territory2 or AccountTeamMember objects.
    Access is primarily based on company ownership and user permissions.
    """

    def __init__(self, client: HubSpotClient):
        self.hubspot_client = client

    async def get_user_account_access(self, user_id: str) -> list[CRMAccountAccessData]:
        logger.info(f"Resolving account access for HubSpot user: {user_id}")

        hubspot_companies = await self._retrieve_hubspot_companies(user_id)

        return [
            CRMAccountAccessData(
                account_id=company["id"],
                account_name=company.get("name", ""),
                access_type=company.get("access_type", "unknown"),
                access_role=company.get("role"),
            )
            for company in hubspot_companies
        ]

    async def _retrieve_hubspot_companies(self, user_id: str) -> list[dict]:
        all_companies = []

        owned_companies = await self._get_owned_companies(user_id)
        all_companies.extend(owned_companies)

        if len(owned_companies) < 10:
            accessible_companies = await self._get_accessible_companies(user_id)
            all_companies.extend(accessible_companies)

        return self._deduplicate_companies(all_companies)

    async def _get_owned_companies(self, user_id: str) -> list[dict]:
        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "hubspot_owner_id",
                        "operator": "EQ",
                        "value": user_id,
                    }
                ]
            }
        ]

        result = await self.hubspot_client.search_objects(
            object_type="company",
            filter_groups=filter_groups,
            properties=["name", "domain", "hubspot_owner_id"],
            limit=100,
        )

        owned_companies = result.get("results", [])

        for company in owned_companies:
            company["access_type"] = "owner"
            company["name"] = company.get("properties", {}).get("name", "")

        logger.info(f"Found {len(owned_companies)} owned companies for user {user_id}")
        return owned_companies

    async def _get_accessible_companies(self, user_id: str) -> list[dict]:
        try:
            result = await self.hubspot_client.list_objects(
                object_type="company",
                properties=["name", "domain", "hubspot_owner_id"],
                limit=100,
            )

            all_companies = result.get("results", [])
            accessible_companies = []

            for company in all_companies:
                properties = company.get("properties", {})
                owner_id = properties.get("hubspot_owner_id")

                if owner_id == user_id:
                    continue

                company["access_type"] = "accessible"
                company["name"] = properties.get("name", "")
                accessible_companies.append(company)

            logger.info(
                f"Found {len(accessible_companies)} accessible companies for user {user_id}"
            )
            return accessible_companies

        except Exception as e:
            logger.warning(f"Error fetching accessible companies for {user_id}: {e}")
            return []

    def _deduplicate_companies(self, all_companies: list[dict]) -> list[dict]:
        unique_companies = {}

        priority = {"owner": 0, "accessible": 1}

        for company in all_companies:
            company_id = company["id"]
            if company_id not in unique_companies:
                unique_companies[company_id] = company
            else:
                current_type = unique_companies[company_id].get("access_type", "")
                new_type = company.get("access_type", "")

                if priority.get(new_type, 99) < priority.get(current_type, 99):
                    unique_companies[company_id] = company

        return list(unique_companies.values())
