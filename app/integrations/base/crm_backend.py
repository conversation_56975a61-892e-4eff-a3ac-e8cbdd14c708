from abc import ABC, abstractmethod
from collections.abc import Callable
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.schemas import CRMMetrics
from app.integrations.types import BackendType


class BaseCRMBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.CRM

    @abstractmethod
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_account(self, account_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_task(self, task_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_tasks_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_event(self, event_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_events_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_events_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_account_access(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def get_metrics(
        self, crm_user_id: str, field_mapping: dict[str, Any] | None = None
    ) -> CRMMetrics:
        pass
