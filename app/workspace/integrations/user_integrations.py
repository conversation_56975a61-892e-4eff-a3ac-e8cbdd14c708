from typing import Any, cast
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.handles import (
    CalendarHandle,
    CRMHandle,
    FileHandle,
    MessagingHandle,
)
from app.workspace.integrations.credentials_resolver import UserCredentialsResolver
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.hubspot_connection import HubSpotConnectionService
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import IntegrationType

IntegrationHandle = CRMHandle | MessagingHandle | FileHandle | CalendarHandle

logger = get_logger()


class UserIntegrations:
    """
    Manages integration access for a specific user in an organization.

    Provides a clean interface to various integration providers (CRM, Messaging, etc.) by
    automatically selecting the appropriate configuration and credentials based on
    the user and organization context.
    """

    def __init__(
        self,
        user_id: UUID,
        environment: OrgEnvironment,
        integration_cfg_service: IntegrationConfigService,
        salesforce_connection_service: SalesforceConnectionService,
        hubspot_connection_service: HubSpotConnectionService,
        db_session: AsyncSession,
    ):
        self.integration_cfg_service = integration_cfg_service
        self.salesforce_connection_service = salesforce_connection_service
        self.hubspot_connection_service = hubspot_connection_service
        self.db_session = db_session
        self.user_id = user_id
        self.environment = environment
        self.org_id = environment.organization_id
        self.env_type = environment.type

        self._factory = self._create_factory()

        self._handles: dict[IntegrationType, Any] = {}
        self._configs: dict[IntegrationType, IntegrationConfig | None] = {}
        self._integration_users: dict[IntegrationType, IntegrationUser | None] = {}

    async def crm(self) -> CRMHandle | None:
        handle = await self._get_handle(IntegrationType.CRM)
        return cast("CRMHandle", handle) if handle else None

    async def get_crm_accounts(self) -> list[dict[str, Any]]:
        crm = await self.crm()
        if not crm:
            return []

        crm_user_id = await self.crm_user_id()
        if not crm_user_id:
            return []

        return await crm.list_account_access(crm_user_id)

    async def sync_crm_accounts(self) -> None:
        crm = await self.crm()
        crm_user_id = await self.crm_user_id()
        if crm and crm_user_id:
            await crm.bulk_sync_account_access(crm_user_ids=[crm_user_id])

    async def crm_user_id(self) -> str | None:
        integration_user = await self._get_integration_user(IntegrationType.CRM)
        return integration_user.external_user_id if integration_user else None

    async def crm_org_id(self) -> str | None:
        integration_user = await self._get_integration_user(IntegrationType.CRM)
        return integration_user.external_org_id if integration_user else None

    async def file(self) -> FileHandle | None:
        handle = await self._get_handle(IntegrationType.FILE)
        return cast("FileHandle", handle) if handle else None

    async def search_files(self, query: str, limit: int = 10) -> list[str]:
        file = await self.file()
        if not file:
            return []

        results = await file.search_files(query, limit)
        return [doc.content for doc, _ in results]

    async def calendar(self) -> CalendarHandle | None:
        handle = await self._get_handle(IntegrationType.CALENDAR)
        return cast("CalendarHandle", handle) if handle else None

    def _create_factory(self) -> IntegrationFactory:
        credentials_resolver = UserCredentialsResolver(
            environment=self.environment,
            user_id=self.user_id,
            integration_config_service=self.integration_cfg_service,
            salesforce_connection_service=self.salesforce_connection_service,
            hubspot_connection_service=self.hubspot_connection_service,
        )

        return create_factory(
            tenant_id=self.environment.id,
            db_session=self.db_session,
            credentials_resolver=credentials_resolver,
        )

    async def _get_handle(
        self, integration_type: IntegrationType
    ) -> IntegrationHandle | None:
        if integration_type in self._handles:
            return self._handles[integration_type]

        try:
            config = await self._get_config(integration_type)
            if not config:
                logger.warning(
                    f"No active {integration_type.value} integration found for organization {self.org_id}"
                )
                return None

            handle: Any
            if integration_type == IntegrationType.CRM:
                integration_user = await self._get_integration_user(IntegrationType.CRM)
                if not integration_user:
                    logger.warning(
                        f"No CRM integration user found for user {self.user_id}, integration {config.id}"
                    )
                    return None

                handle = self._factory.crm(config.source)
            elif integration_type == IntegrationType.FILE:
                handle = self._factory.file(config.source)
            elif integration_type == IntegrationType.CALENDAR:
                handle = self._factory.calendar(config.source)
            else:
                raise ValueError(
                    f"Provider type {integration_type} not yet implemented"
                )

            if handle is not None:
                self._handles[integration_type] = handle

            return handle

        except Exception:
            logger.exception(f"Error initializing {integration_type.value} provider")
            return None

    async def _get_config(
        self, integration_type: IntegrationType
    ) -> IntegrationConfig | None:
        if integration_type in self._configs:
            return self._configs[integration_type]

        config = (
            await self.integration_cfg_service.get_user_activated_integration_config(
                environment=self.environment,
                integration_type=integration_type,
                user_id=self.user_id,
            )
        )

        if config is not None:
            self._configs[integration_type] = config
        else:
            logger.warning(
                f"No activated {integration_type.value} integration found for user {self.user_id}"
            )

        return config

    async def _get_integration_user(
        self, integration_type: IntegrationType
    ) -> IntegrationUser | None:
        if integration_type in self._integration_users:
            return self._integration_users[integration_type]

        config = await self._get_config(integration_type)
        if not config:
            return None

        integration_user = await self.integration_cfg_service.get_integration_user(
            integration_config_id=config.id, user_id=self.user_id
        )

        if integration_user is not None:
            self._integration_users[integration_type] = integration_user

        return integration_user


async def create_user_integrations(
    user_id: UUID,
    environment_id: UUID,
    db_session: AsyncSession,
) -> UserIntegrations:
    env_repo = EnvironmentRepository(db_session)
    environment = await env_repo.get_by_id(environment_id)

    integration_cfg_repo = IntegrationConfigRepository(db_session)
    integration_user_repo = IntegrationUserRepository(db_session)

    integration_cfg_service = IntegrationConfigService(
        integration_cfg_repo=integration_cfg_repo,
        integration_user_repo=integration_user_repo,
    )
    salesforce_connection_service = SalesforceConnectionService(
        db_session=db_session,
        integration_user_repo=integration_user_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.salesforce.auth_url),
        token_url=str(config.salesforce.token_url),
        redirect_uri=str(config.salesforce.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )

    hubspot_connection_service = HubSpotConnectionService(
        db_session=db_session,
        integration_user_repo=integration_user_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.hubspot.auth_url),
        token_url=str(config.hubspot.token_url),
        redirect_uri=str(config.hubspot.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )

    return UserIntegrations(
        user_id=user_id,
        environment=OrgEnvironment.model_validate(environment),
        integration_cfg_service=integration_cfg_service,
        salesforce_connection_service=salesforce_connection_service,
        hubspot_connection_service=hubspot_connection_service,
        db_session=db_session,
    )
