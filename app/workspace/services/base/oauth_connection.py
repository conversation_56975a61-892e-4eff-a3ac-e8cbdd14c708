import uuid
from abc import ABC, abstractmethod
from datetime import UTC, datetime, timedelta
from typing import Generic, TypeVar

from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OA<PERSON><PERSON>lowManager, OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.schemas.integration import BaseOAuthCredentials

logger = get_logger()


TCredentials = TypeVar("TCredentials", bound=BaseOAuthCredentials)
TTokenResponse = TypeVar("TTokenResponse", bound=BaseModel)


class BaseOAuthConnection(Generic[TCredentials, TTokenResponse], ABC):
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,
    ):
        self.db_session = db_session
        self.integration_user_repo = integration_user_repo
        self.integration_cfg_repo = integration_cfg_repo
        self.auth_url = auth_url
        self.token_url = token_url
        self.redirect_uri = redirect_uri
        self.flow_type = flow_type
        self.oauth_flow_manager = OAuthFlowManager()

    @property
    @abstractmethod
    def integration_source(self) -> IntegrationSource:
        """Return the integration source for this service."""

    @property
    @abstractmethod
    def default_scope(self) -> str:
        """Return the default OAuth scope for this integration."""

    @property
    @abstractmethod
    def default_token_expiry_seconds(self) -> int:
        """Return the default token expiry time in seconds."""

    @property
    def token_endpoint_auth_method(self) -> str:
        """Return the token endpoint authentication method. Defaults to client_secret_basic."""
        return "client_secret_basic"

    @abstractmethod
    async def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, TCredentials]:
        """Get integration config and parse credentials for this provider."""

    @abstractmethod
    def _validate_credentials(self, credentials: TCredentials) -> None:
        """Validate that required credentials are present."""

    @abstractmethod
    async def _extract_user_info_from_token(
        self, token_data: dict, credentials: TCredentials
    ) -> dict[str, str]:
        """
        Extract user information from token response.

        Should return a dict with keys like:
        - external_user_id: str
        - external_org_id: str (optional)
        """

    @abstractmethod
    def _get_redirect_uri_for_token_exchange(self) -> str:
        """Get the redirect URI to use for token exchange (may differ from auth redirect)."""

    @abstractmethod
    def _create_token_response(
        self, integration_user, expires_at: datetime
    ) -> TTokenResponse:
        """Create the provider-specific token response object."""

    async def generate_oauth_authorization_uri(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        scope: str | None = None,
    ) -> str:
        """Generate OAuth authorization URI."""
        _, credentials = await self._get_config_and_credentials(environment)
        self._validate_credentials(credentials)

        return self.oauth_flow_manager.generate_authorization_uri(
            uid=str(user_id),
            client_id=credentials.client_id,
            redirect_uri=self.redirect_uri,
            auth_url=self.auth_url,
            flow_type=self.flow_type,
            scope=scope or self.default_scope,
        )

    async def process_oauth_callback(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        code: str,
        state: str,
    ) -> TTokenResponse:
        """Process OAuth callback and create/update integration user."""
        integration_config, credentials = await self._get_config_and_credentials(
            environment
        )
        self._validate_credentials(credentials)

        token_data = await self.oauth_flow_manager.exchange_code_for_token(
            code=code,
            state=state,
            expected_uid=str(user_id),
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            redirect_uri=self._get_redirect_uri_for_token_exchange(),
            token_url=self.token_url,
            token_endpoint_auth_method=self.token_endpoint_auth_method,
        )

        user_info = await self._extract_user_info_from_token(token_data, credentials)

        expires_in = token_data.get("expires_in", self.default_token_expiry_seconds)
        now = datetime.now(UTC)
        expires_at = now + timedelta(seconds=int(expires_in))

        integration_user = await self._create_or_update_integration_user(
            user_id=user_id,
            integration_config=integration_config,
            token_data=token_data,
            user_info=user_info,
            expires_at=expires_at,
            now=now,
        )

        await self.db_session.commit()
        await self.db_session.refresh(integration_user)

        return self._create_token_response(integration_user, expires_at)

    async def refresh_access_token(
        self,
        integration_user_id: uuid.UUID,
        environment: OrgEnvironment,
    ) -> TTokenResponse:
        """Refresh access token for an existing integration user."""
        integration_user = await self.integration_user_repo.get_by_id(
            integration_user_id
        )
        if not integration_user:
            raise IntegrationTokenNotFoundError()

        _, credentials = await self._get_config_and_credentials(environment)
        self._validate_credentials(credentials)

        token_data = await self.oauth_flow_manager.refresh_access_token(
            refresh_token=integration_user.refresh_token,
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            token_url=self.token_url,
            token_endpoint_auth_method=self.token_endpoint_auth_method,
        )

        expires_in = token_data.get("expires_in", self.default_token_expiry_seconds)
        now = datetime.now(UTC)
        expires_at = now + timedelta(seconds=int(expires_in))

        update_data = {
            "access_token": token_data.get("access_token"),
            "expires_at": expires_at,
            "last_refreshed_at": now,
        }

        for field in ["refresh_token", "token_type", "scope", "instance_url"]:
            if field in token_data:
                update_data[field] = token_data.get(field)

        print("-" * 80)
        print("update_data: ", update_data)
        print("-" * 80)

        updated_integration_user = await self.integration_user_repo.update(
            integration_user.id, **update_data
        )
        await self.db_session.commit()
        await self.db_session.refresh(updated_integration_user)

        return self._create_token_response(updated_integration_user, expires_at)

    async def remove_connection(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
    ) -> None:
        """Remove OAuth connection for a user."""
        integration_config, _ = await self._get_config_and_credentials(environment)

        integration_user = await self.integration_user_repo.get_by_user_and_integration(
            user_id=user_id, integration_config_id=integration_config.id
        )

        if not integration_user:
            raise IntegrationTokenNotFoundError(
                f"No {self.integration_source.value} connection found for this user"
            )

        await self.integration_user_repo.delete(integration_user.id)
        await self.db_session.commit()

        logger.info(
            f"Removed {self.integration_source.value} connection for user {user_id} "
            f"in organization {environment.organization_id}"
        )

    async def _create_or_update_integration_user(
        self,
        user_id: uuid.UUID,
        integration_config: IntegrationConfig,
        token_data: dict,
        user_info: dict[str, str],
        expires_at: datetime,
        now: datetime,
    ):
        """Create or update integration user with token data."""
        integration_user = await self.integration_user_repo.get_by_user_and_integration(
            user_id=user_id, integration_config_id=integration_config.id
        )

        base_data = {
            "access_token": token_data.get("access_token"),
            "refresh_token": token_data.get("refresh_token"),
            "instance_url": token_data.get("instance_url"),
            "scope": token_data.get("scope"),
            "token_type": token_data.get("token_type"),
            "expires_at": expires_at,
            "last_refreshed_at": now,
            "external_user_id": user_info.get("external_user_id"),
        }

        if "external_org_id" in user_info:
            base_data["external_org_id"] = user_info.get("external_org_id")

        if integration_user:
            return await self.integration_user_repo.update(
                integration_user.id, **base_data
            )

        return await self.integration_user_repo.create(
            user_id=user_id,
            integration_config_id=integration_config.id,
            **base_data,
        )
