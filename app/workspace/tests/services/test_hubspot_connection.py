from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.oauth.flow_manager import OAuthFlowType
from app.workspace.exceptions import IntegrationCredentialsError
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import HubSpotCredentials
from app.workspace.services.hubspot_connection import HubSpotConnectionService


@pytest.fixture
def hubspot_connection_service():
    return HubSpotConnectionService(
        db_session=Mock(spec=AsyncSession),
        integration_user_repo=Mock(spec=IntegrationUserRepository),
        integration_cfg_repo=Mock(spec=IntegrationConfigRepository),
        auth_url="https://app.hubspot.com/oauth/authorize",
        token_url="https://api.hubapi.com/oauth/v1/token",
        redirect_uri="http://localhost:8000/hubspot/callback",
        flow_type=OAuthFlowType.STANDARD,
    )


class TestHubSpotConnectionService:
    def test_integration_source(self, hubspot_connection_service):
        from app.integrations.types import IntegrationSource

        assert (
            hubspot_connection_service.integration_source == IntegrationSource.HUBSPOT
        )

    def test_default_scope(self, hubspot_connection_service):
        expected_scope = "crm.objects.contacts.read crm.objects.companies.read crm.objects.deals.read crm.objects.leads.read crm.objects.custom.read crm.objects.users.read"
        assert hubspot_connection_service.default_scope == expected_scope

    def test_default_token_expiry_seconds(self, hubspot_connection_service):
        assert hubspot_connection_service.default_token_expiry_seconds == 1800

    def test_token_endpoint_auth_method(self, hubspot_connection_service):
        assert (
            hubspot_connection_service.token_endpoint_auth_method
            == "client_secret_post"
        )

    def test_validate_credentials_success(self, hubspot_connection_service):
        credentials = HubSpotCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        # Should not raise any exception
        hubspot_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_id(self, hubspot_connection_service):
        credentials = HubSpotCredentials(
            client_id="",
            client_secret="test_client_secret",  # noqa: S106
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing HubSpot client_id and client_secret",
        ):
            hubspot_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_secret(
        self, hubspot_connection_service
    ):
        credentials = HubSpotCredentials(
            client_id="test_client_id",
            client_secret="",
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing HubSpot client_id and client_secret",
        ):
            hubspot_connection_service._validate_credentials(credentials)

    @pytest.mark.anyio
    async def test_extract_user_info_from_token_success(
        self, mocker, hubspot_connection_service
    ):
        # Mock the HubSpot client
        mock_client = Mock()
        mock_client.get_user_info = AsyncMock(
            return_value={
                "user_id": 12345,
                "hub_id": 67890,
                "app_id": 111111,
                "expires_in": 1754,
            }
        )

        mocker.patch(
            "app.workspace.services.hubspot_connection.HubSpotClient",
            return_value=mock_client,
        )

        credentials = HubSpotCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        token_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
        }

        result = await hubspot_connection_service._extract_user_info_from_token(
            token_data, credentials
        )

        assert result == {
            "external_user_id": "12345",
            "external_org_id": "67890",
        }

        # Verify the client was created with the correct access token
        mock_client.get_user_info.assert_called_once()

    def test_get_redirect_uri_for_token_exchange(self, hubspot_connection_service):
        result = hubspot_connection_service._get_redirect_uri_for_token_exchange()
        assert result == "http://localhost:8000/hubspot/callback"
